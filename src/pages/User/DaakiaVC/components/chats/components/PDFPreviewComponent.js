import { useState } from 'react';
import { Document, Page } from 'react-pdf';
import { MdOutlineZoomIn, MdOutlineZoomOut, MdZoomInMap, MdNavigateBefore, MdNavigateNext } from 'react-icons/md';
import './PDFPreviewComponent.scss';

/**
 * Reusable PDF Preview Component with Controls
 * Handles PDF rendering, page navigation, and zoom functionality in a centered frame
 */
export function PDFPreviewComponent({
  fileUrl,
  onLoadSuccess,
  className = "pdf-preview-container"
}) {
  // PDF-specific state
  const [numPages, setNumPages] = useState(null);
  const [pageNumber, setPageNumber] = useState(1);
  const [pdfScale, setPdfScale] = useState(0.5);

  // PDF-specific functions
  const handleDocumentLoadSuccess = ({ numPages: totalPages }) => {
    setNumPages(totalPages);
    setPageNumber(1);
    // Call parent callback if provided
    if (onLoadSuccess) {
      onLoadSuccess({ numPages: totalPages });
    }
  };

  // PDF zoom controls
  const pdfZoomIn = () => setPdfScale((prev) => Math.min(prev + 0.2, 3));
  const pdfZoomOut = () => setPdfScale((prev) => Math.max(prev - 0.2, 0.3));
  const resetPdfZoom = () => setPdfScale(0.5);

  // PDF navigation
  const goToPrevPage = () => setPageNumber((prev) => Math.max(prev - 1, 1));
  const goToNextPage = () => setPageNumber((prev) => Math.min(prev + 1, numPages || 1));

  return (
    <div className={className}>
      <div className="pdf-frame">
        {/* Header */}
        <div className="pdf-header">
          <h3 className="pdf-title">PDF Preview</h3>
          {numPages && (
            <span className="pdf-page-info">
              Page {pageNumber} of {numPages}
            </span>
          )}
        </div>

        {/* PDF Content */}
        <div className="pdf-content">
          <Document
            file={fileUrl}
            onLoadSuccess={handleDocumentLoadSuccess}
            loading={<div className="pdf-loading">Loading PDF...</div>}
            error={
              <div className="pdf-error">
                <div>Failed to load PDF</div>
                <a
                  href={fileUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="pdf-error-link"
                >
                  Open in new tab
                </a>
              </div>
            }
            className="pdf-document"
          >
            <Page
              pageNumber={pageNumber}
              scale={pdfScale}
              className="pdf-page"
              renderAnnotationLayer={false}
              renderTextLayer={false}
              width={window.innerWidth * 0.3}
            />
          </Document>
        </div>

        {/* Controls Footer */}
        <div className="pdf-controls">
          {/* Page Navigation */}
          {numPages && numPages > 1 && (
            <div className="pdf-navigation">
              <button
                onClick={goToPrevPage}
                disabled={pageNumber <= 1}
                className={`pdf-nav-button ${pageNumber <= 1 ? 'disabled' : ''}`}
              >
                <MdNavigateBefore size={16} />
                Previous
              </button>

              <button
                onClick={goToNextPage}
                disabled={pageNumber >= numPages}
                className={`pdf-nav-button ${pageNumber >= numPages ? 'disabled' : ''}`}
              >
                Next
                <MdNavigateNext size={16} />
              </button>
            </div>
          )}

          {/* Zoom Controls */}
          <div className="pdf-zoom-controls">
            <button
              onClick={pdfZoomOut}
              className="pdf-zoom-button"
              title="Zoom Out"
            >
              <MdOutlineZoomOut size={18} />
            </button>

            <span className="pdf-zoom-level">
              {Math.round(pdfScale * 100)}%
            </span>

            <button
              onClick={pdfZoomIn}
              className="pdf-zoom-button"
              title="Zoom In"
            >
              <MdOutlineZoomIn size={18} />
            </button>

            <button
              onClick={resetPdfZoom}
              className="pdf-zoom-button"
              title="Reset Zoom"
            >
              <MdZoomInMap size={18} />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

export default PDFPreviewComponent;
