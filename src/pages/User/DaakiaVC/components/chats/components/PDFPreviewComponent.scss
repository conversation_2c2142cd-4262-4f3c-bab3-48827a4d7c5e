// PDF Preview Component Styles
.pdf-preview-container {
  width: 100%;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  box-sizing: border-box;
}

.pdf-frame {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  max-width: 95vw;
  max-height: 95vh;
  background-color: #f8f9fa;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
  border: 1px solid #e1e5e9;
  overflow: hidden;
  position: relative;
}

.pdf-header {
  width: 100%;
  padding: 8px 16px;
  background-color: #e9ecef;
  border-bottom: 1px solid #d1d9e0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
  min-height: 40px;
}

.pdf-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #24292f;
}

.pdf-page-info {
  font-size: 14px;
  color: #656d76;
  font-weight: 500;
}

.pdf-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 10px;
  overflow: auto;
  width: 100%;
  background-color: #ffffff;
}

.pdf-document {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.pdf-page {
  max-width: 100%;
  max-height: 100%;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-radius: 4px;
}

.pdf-loading {
  padding: 40px;
  color: #656d76;
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
}

.pdf-error {
  padding: 40px;
  color: #d1242f;
  font-size: 16px;
  text-align: center;
  
  div {
    margin-bottom: 12px;
    font-weight: 500;
  }
}

.pdf-error-link {
  color: #0969da;
  text-decoration: none;
  font-size: 14px;
  font-weight: 500;
  
  &:hover {
    text-decoration: underline;
  }
}

.pdf-controls {
  width: 100%;
  padding: 8px 16px;
  background-color: #e9ecef;
  border-top: 1px solid #d1d9e0;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
  flex-shrink: 0;
  min-height: 40px;
}

.pdf-navigation {
  display: flex;
  align-items: center;
  gap: 8px;
}

.pdf-nav-button {
  padding: 8px 16px;
  border: 1px solid #d0d7de;
  border-radius: 6px;
  background-color: #ffffff;
  color: #24292f;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 6px;
  
  &:hover:not(.disabled) {
    background-color: #f3f4f6;
    border-color: #9ca3af;
  }
  
  &.disabled {
    background-color: #f6f8fa;
    color: #8c959f;
    cursor: not-allowed;
    border: 1px solid #d1d9e0;
  }
}

.pdf-zoom-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.pdf-zoom-button {
  padding: 8px;
  border: 1px solid #d0d7de;
  border-radius: 6px;
  background-color: #ffffff;
  color: #24292f;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  width: 36px;
  height: 36px;
  
  &:hover {
    background-color: #f3f4f6;
    border-color: #9ca3af;
  }
}

.pdf-zoom-level {
  min-width: 60px;
  text-align: center;
  font-size: 14px;
  font-weight: 500;
  color: #24292f;
}

// Responsive design
@media (max-width: 768px) {
  .pdf-preview-container {
    padding: 10px;
  }
  
  .pdf-frame {
    max-width: 100vw;
    max-height: 100vh;
    border-radius: 8px;
  }
  
  .pdf-header,
  .pdf-controls {
    padding: 6px 12px;
  }
  
  .pdf-title {
    font-size: 14px;
  }
  
  .pdf-page-info {
    font-size: 12px;
  }
  
  .pdf-nav-button {
    padding: 6px 12px;
    font-size: 12px;
  }
  
  .pdf-zoom-button {
    width: 32px;
    height: 32px;
  }
  
  .pdf-zoom-level {
    font-size: 12px;
    min-width: 50px;
  }
}

@media (max-width: 480px) {
  .pdf-controls {
    flex-direction: column;
    gap: 8px;
  }
  
  .pdf-navigation,
  .pdf-zoom-controls {
    width: 100%;
    justify-content: center;
  }
}
